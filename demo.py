#!/usr/bin/env python3
"""
🚀 AI Utility Orchestrator - Complete Demo
===========================================

This demo showcases the key features of the AI Utility Orchestrator:
- 100% Dynamic configuration (zero hardcoding)
- AI-powered tool selection
- Context-aware operations
- Multi-agent orchestration patterns
- Universal tool integration

Author: <PERSON><PERSON><PERSON>
"""

import os
import json
from typing import Dict, Any
from ai_utility_orchestrator import (
    agent_executor, 
    ContextManager, 
    ToolRegistry, 
    create_tool_from_function,
    ConfigUtils
)

def setup_environment():
    """Setup OpenAI API key - replace with your actual key"""
    # Option 1: Set directly (for demo)
    os.environ["OPENAI_API_KEY"] = "your-api-key-here"
    
    # Option 2: Load from .env file (recommended)
    # from dotenv import load_dotenv
    # load_dotenv()
    
    print("🔑 Environment configured")

def create_dynamic_config() -> Dict[str, Any]:
    """
    Create completely dynamic configuration.
    This showcases the zero-hardcoding principle - everything is user-defined.
    """
    return {
        # LLM Configuration - User's choice
        "llm": {
            "model": "gpt-4o-mini",  # Could be any model: gpt-4, claude-3, etc.
            "temperature": 0.7       # User controls creativity
        },
        
        # System Behavior - Fully customizable
        "system_prompt": "You are an intelligent AI orchestrator that selects the best tools for user requests.",
        "context_limit": 10,
        "context_format": {
            "user_role": "user",
            "assistant_role": "assistant", 
            "include_metadata": True
        },
        
        # AI Decision Making Prompts - Complete user control
        "no_tools_prompt": """
        User request: "{user_input}"
        
        No tools are available for this request. Provide a helpful direct response
        explaining what you understand about their request and suggest alternatives.
        """,
        
        "tool_selection_prompt": """
        🤖 AI Tool Orchestrator - Intelligent Selection
        
        Available tools:
        {tools_text}
        
        User request: "{user_input}"
        Context: {context_text}
        
        Analyze the request and select the most appropriate tool:
        1. Match user intent to tool capabilities
        2. Extract required parameters
        3. Consider context and user preferences
        
        Respond in JSON format:
        {{"selected_tool": "tool_name_or_none", "parameters": {{}}, "reasoning": "detailed_explanation", "confidence": 0.95}}
        """,
        
        "tool_description_template": "🔧 {name}: {description}",
        
        # Error Handling - User-defined responses
        "error_response_template": "❌ Tool '{tool_name}' encountered an error: {error}",
        "llm_error_message": "🤔 I'm experiencing some technical difficulties. Please try again.",
        "general_error_message": "⚠️ An unexpected error occurred: {error}",
        
        # Tools will be added dynamically
        "tools": []
    }

def create_business_tools():
    """
    Create realistic business tools to showcase versatility.
    Each tool demonstrates different capabilities and use cases.
    """
    
    def advanced_calculator(params):
        """Advanced calculator with multiple operations"""
        expression = params.get("expression", "0")
        operation_type = params.get("type", "basic")
        
        try:
            if operation_type == "financial":
                # Simulate financial calculations
                result = eval(expression)
                return f"💰 Financial calculation result: ${result:,.2f}"
            elif operation_type == "scientific":
                # Simulate scientific calculations
                import math
                # Add math functions to eval context (be careful in production!)
                result = eval(expression, {"__builtins__": {}, "math": math})
                return f"🔬 Scientific calculation result: {result}"
            else:
                result = eval(expression)
                return f"🧮 Calculation result: {result}"
        except Exception as e:
            return f"❌ Calculation error: {e}"
    
    def data_analyzer(params):
        """Simulate data analysis capabilities"""
        data_type = params.get("data_type", "general")
        query = params.get("query", "")
        
        analyses = {
            "sales": f"📊 Sales Analysis: Based on '{query}', revenue trends show 15% growth",
            "customer": f"👥 Customer Analysis: '{query}' indicates 85% satisfaction rate",
            "market": f"📈 Market Analysis: '{query}' suggests emerging opportunities",
            "general": f"📋 Data Analysis: Processed '{query}' - insights available"
        }
        
        return analyses.get(data_type, analyses["general"])
    
    def workflow_orchestrator(params):
        """Demonstrate multi-step workflow orchestration"""
        workflow_type = params.get("workflow", "standard")
        steps = params.get("steps", [])
        
        if workflow_type == "data_pipeline":
            return "🔄 Data Pipeline executed: Extract → Transform → Load → Analyze ✅"
        elif workflow_type == "approval":
            return "✅ Approval Workflow: Request → Review → Approve → Notify ✅"
        else:
            return f"⚙️ Custom Workflow executed with {len(steps)} steps ✅"
    
    def smart_assistant(params):
        """Context-aware assistant tool"""
        context_manager = params.get("context_manager")
        task = params.get("task", "general")
        
        if context_manager:
            user_prefs = context_manager.get_context("user_preferences", {})
            theme = user_prefs.get("theme", "default")
            return f"🤖 Smart Assistant ({theme} mode): Handling '{task}' with personalized approach"
        
        return f"🤖 Smart Assistant: Handling '{task}'"
    
    return [
        ("advanced_calculator", advanced_calculator, "Performs advanced mathematical and financial calculations", {
            "type": "object",
            "properties": {
                "expression": {"type": "string", "description": "Mathematical expression"},
                "type": {"type": "string", "enum": ["basic", "financial", "scientific"]}
            },
            "required": ["expression"]
        }),
        
        ("data_analyzer", data_analyzer, "Analyzes various types of business data", {
            "type": "object", 
            "properties": {
                "data_type": {"type": "string", "enum": ["sales", "customer", "market", "general"]},
                "query": {"type": "string", "description": "Analysis query or dataset description"}
            },
            "required": ["query"]
        }),
        
        ("workflow_orchestrator", workflow_orchestrator, "Orchestrates complex business workflows", {
            "type": "object",
            "properties": {
                "workflow": {"type": "string", "enum": ["data_pipeline", "approval", "custom"]},
                "steps": {"type": "array", "items": {"type": "string"}}
            },
            "required": ["workflow"]
        }),
        
        ("smart_assistant", smart_assistant, "Context-aware personal assistant", {
            "type": "object",
            "properties": {
                "task": {"type": "string", "description": "Task to be handled"}
            },
            "required": ["task"]
        })
    ]

def setup_context_manager():
    """Setup context manager with realistic business context"""
    cm = ContextManager()
    
    # User preferences
    cm.set_context("user_preferences", {
        "theme": "professional",
        "language": "en",
        "timezone": "UTC",
        "notification_level": "high"
    })
    
    # Business context
    cm.set_context("business_context", {
        "department": "Analytics",
        "role": "Data Scientist", 
        "project": "Q4 Revenue Analysis",
        "access_level": "senior"
    })
    
    # Session info
    cm.set_context("session_info", {
        "session_id": "demo_2024",
        "start_time": "2024-01-15T10:00:00Z"
    })
    
    print("🧠 Context manager initialized with business context")
    return cm

def demonstrate_core_features(config, context_manager):
    """Demonstrate core orchestration features"""
    
    print("\n" + "="*80)
    print("🎯 CORE FEATURES DEMONSTRATION")
    print("="*80)
    
    # Feature 1: AI-Powered Tool Selection
    queries = [
        ("🧮 Mathematical Query", "Calculate the compound interest: 10000 * (1.05)^5"),
        ("📊 Data Analysis Query", "Analyze our Q4 sales performance data"),
        ("⚙️ Workflow Query", "Set up a data pipeline workflow for customer analytics"),
        ("🤖 Assistant Query", "Help me organize my daily tasks")
    ]
    
    for category, query in queries:
        print(f"\n{category}")
        print(f"Query: {query}")
        
        result = agent_executor(
            user_input=query,
            config=config,
            user_id="demo_user",
            context_manager=context_manager
        )
        
        print(f"✅ Response: {result['final_response']}")
        print(f"🔧 Tool Selected: {result['selected_tool']}")
        print(f"📋 Parameters: {json.dumps(result['tool_parameters'], indent=2)}")
        print("-" * 60)

def demonstrate_advanced_patterns(config, context_manager):
    """Demonstrate advanced orchestration patterns"""
    
    print("\n" + "="*80)
    print("🚀 ADVANCED PATTERNS DEMONSTRATION") 
    print("="*80)
    
    # Pattern 1: Context-Aware Operations
    print("\n🧠 Pattern 1: Context-Aware Operations")
    context_manager.set_context("current_project", "Revenue Optimization")
    
    result = agent_executor(
        "Help me with my current project analysis",
        config=config,
        context_manager=context_manager
    )
    print(f"Context-aware response: {result['final_response']}")
    
    # Pattern 2: Dynamic Tool Registration
    print("\n🔧 Pattern 2: Dynamic Tool Registration")
    
    def emergency_tool(params):
        return f"🚨 Emergency protocol activated for: {params.get('issue', 'unknown')}"
    
    # Add tool dynamically
    registry = ToolRegistry()
    for tool in config["tools"]:
        registry.register_tool(tool)
    
    emergency = create_tool_from_function(
        emergency_tool,
        "emergency_handler", 
        "Handles emergency situations",
        {"type": "object", "properties": {"issue": {"type": "string"}}, "required": ["issue"]}
    )
    registry.register_tool(emergency)
    
    # Update config with new tool
    config["tools"] = registry.get_tools()
    
    result = agent_executor(
        "We have a system outage emergency!",
        config=config,
        context_manager=context_manager
    )
    print(f"Dynamic tool response: {result['final_response']}")

def main():
    """Main demo execution"""
    print("🚀 AI Utility Orchestrator - Complete Demo")
    print("=" * 50)
    
    # Setup
    setup_environment()
    config = create_dynamic_config()
    
    # Create and register tools
    print("\n🔧 Creating dynamic tool registry...")
    registry = ToolRegistry()
    
    for name, func, desc, schema in create_business_tools():
        tool = create_tool_from_function(func, name, desc, schema)
        registry.register_tool(tool)
        print(f"   ✅ Registered: {name}")
    
    config["tools"] = registry.get_tools()
    
    # Setup context
    context_manager = setup_context_manager()
    
    # Run demonstrations
    demonstrate_core_features(config, context_manager)
    demonstrate_advanced_patterns(config, context_manager)
    
    print("\n" + "="*80)
    print("🎉 DEMO COMPLETE - AI Utility Orchestrator Successfully Demonstrated!")
    print("="*80)
    print("\nKey Features Showcased:")
    print("✅ 100% Dynamic Configuration (Zero Hardcoding)")
    print("✅ AI-Powered Intelligent Tool Selection") 
    print("✅ Context-Aware Operations")
    print("✅ Universal Tool Integration")
    print("✅ Advanced Orchestration Patterns")
    print("✅ Real-time Tool Registration")
    print("\n🚀 Ready for production use in any AI project!")

if __name__ == "__main__":
    main()
