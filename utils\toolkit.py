# ai_utility_orchestrator/utils/toolkit.py

import os, json, logging, importlib
from typing import Dict, List, Callable, Any
from pathlib import Path

try:
    from importlib import resources
except ImportError:
    # Python < 3.9 fallback
    import importlib_resources as resources

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

try:
    from openai import OpenAI
except ImportError:
    OpenAI = None

logger = logging.getLogger("ai_utility_orchestrator.toolkit")

class ConfigUtils:
    @staticmethod
    def _get_default_config() -> Dict[str, Any]:
        """Get empty configuration - user must provide all configuration."""
        return {}

    @staticmethod
    def load_config(file_path: str = None, overrides: dict = None, user_id: str = None) -> Dict[str, Any]:
        config = None
        config_path = None

        # Case 1: Load from specified file path
        if file_path:
            config_path = file_path
            if os.path.exists(file_path):
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        config = json.load(f)
                except (json.JSONDecodeError, IOError) as e:
                    logger.error(f"Failed to load config from {file_path}: {e}")
                    config = None
            else:
                logger.error(f"Config file not found: {file_path}")
                config = None

        # Case 2: Load from environment variable or user-provided path only
        else:
            # Only check environment variable path - no default config files
            env_config = os.getenv("AI_ORCHESTRATOR_CONFIG")
            if env_config and os.path.exists(env_config):
                try:
                    with open(env_config, "r", encoding="utf-8") as f:
                        config = json.load(f)
                        config_path = env_config
                except (json.JSONDecodeError, IOError) as e:
                    logger.warning(f"Failed to load config from environment path {env_config}: {e}")
                    config = None

        # If no config was loaded, use minimal dynamic configuration
        if config is None:
            logger.info("No config file found. Using minimal dynamic configuration.")
            config = ConfigUtils._get_default_config()

        # Apply user-specific config if available
        if user_id and config_path and config_path != "package_resources":
            try:
                user_config_path = Path(config_path).parent / f"user_{user_id}.json"
                if user_config_path.exists():
                    with open(user_config_path, "r", encoding="utf-8") as f:
                        user_config = json.load(f)
                        # Deep merge user config
                        ConfigUtils._deep_merge(config, user_config)
            except Exception as e:
                logger.warning(f"Could not load user config for {user_id}: {e}")

        # Apply overrides
        if overrides:
            ConfigUtils._deep_merge(config, overrides)

        return config

    @staticmethod
    def _deep_merge(base_dict: dict, update_dict: dict) -> None:
        """Deep merge update_dict into base_dict."""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                ConfigUtils._deep_merge(base_dict[key], value)
            else:
                base_dict[key] = value

    @staticmethod
    def load_tools_from_config(config: Dict[str, Any]) -> List[Dict[str, Any]]:
        return config.get("tools", [])

class EnvUtils:
    @staticmethod
    def get_env(key: str, default: str = "", required: bool = False) -> str:
        value = os.getenv(key, default)
        if required and not value:
            raise EnvironmentError(f"Missing required environment variable: {key}")
        return value


class DynamicImportUtils:
    @staticmethod
    def load_object(path: str) -> Any:
        try:
            module_path, attr = path.rsplit(".", 1)
            module = importlib.import_module(module_path)
            return getattr(module, attr)
        except Exception as e:
            logger.error(f"Could not import {path}: {e}")
            return None

class RegistryUtils:
    registered_tools: Dict[str, Callable] = {}

    @classmethod
    def register_tool(cls, name: str):
        def wrapper(func: Callable):
            cls.registered_tools[name] = func
            logger.info(f"Registered tool: {name}")
            return func
        return wrapper

    @classmethod
    def get_tool(cls, name: str) -> Callable:
        return cls.registered_tools.get(name)

    @classmethod
    def list_registered_tools(cls) -> List[str]:
        return list(cls.registered_tools.keys())
