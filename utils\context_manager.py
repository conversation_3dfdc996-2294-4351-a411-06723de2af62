# ai_utility_orchestrator/utils/context_manager.py

import json
import os
from pathlib import Path
 
class ContextManager:

    def __init__(self, history_path=None):
        # Use default path if none provided
        if history_path is None:
            # Use user's home directory for data storage when installed as package
            home_dir = Path.home()
            data_dir = home_dir / ".ai_utility_orchestrator"
            history_path = data_dir / "chat_history.json"

        self.history_path = Path(history_path)

        # Create directory if it doesn't exist
        self.history_path.parent.mkdir(parents=True, exist_ok=True)

        self.history = self._load_history()

        # Runtime context for session-level state management
        self.runtime_context = {}
 
    def _load_history(self):

        if self.history_path.exists():

            with open(self.history_path, "r", encoding="utf-8") as f:

                return json.load(f)

        return {}
 
    def _save_history(self):

        with open(self.history_path, "w", encoding="utf-8") as f:

            json.dump(self.history, f, indent=4)
 
    def get_history(self, user_id):

        return self.history.get(user_id, [])

    def get_recent_messages(self, user_id, limit=5, format_config=None):
        """Get recent messages formatted for LLM context with configurable roles."""
        history = self.get_history(user_id)
        messages = []

        # Get configurable role names - must be provided by user
        if not format_config:
            raise ValueError("format_config must be provided with user_role, assistant_role, and include_metadata")

        if not isinstance(format_config, dict):
            raise ValueError("format_config must be a dictionary containing user_role, assistant_role, and include_metadata")

        user_role = format_config.get("user_role")
        assistant_role = format_config.get("assistant_role")
        include_metadata = format_config.get("include_metadata")

        if user_role is None or assistant_role is None or include_metadata is None:
            raise ValueError("format_config must contain user_role, assistant_role, and include_metadata")

        # Get last 'limit' interactions
        recent_history = history[-limit:] if len(history) > limit else history

        for i, interaction in enumerate(recent_history):
            user_msg = {"role": user_role, "content": interaction["user"]}
            assistant_msg = {"role": assistant_role, "content": interaction["bot"]}

            # Add metadata if requested
            if include_metadata:
                user_msg["metadata"] = {"interaction_index": i, "user_id": user_id}
                assistant_msg["metadata"] = {"interaction_index": i, "user_id": user_id}

            messages.append(user_msg)
            messages.append(assistant_msg)

        return messages
 
    def add_interaction(self, user_id, user_input, bot_response):

        if user_id not in self.history:

            self.history[user_id] = []

        # Use standard keys for simplicity - users can configure display format elsewhere
        self.history[user_id].append({
            "user": user_input,
            "bot": bot_response
        })

        self._save_history()
 
    def clear_history(self, user_id=None):

        if user_id:

            self.history[user_id] = []

        else:

            self.history = {}

        self._save_history()

    # Runtime Context Management Methods
    def set_context(self, key: str, value: any) -> None:
        """Set a runtime context value for the current session."""
        self.runtime_context[key] = value

    def get_context(self, key: str, default=None) -> any:
        """Get a runtime context value by key."""
        return self.runtime_context.get(key, default)

    def get_all_context(self) -> dict:
        """Get all runtime context data."""
        return self.runtime_context.copy()

    def clear_context(self) -> None:
        """Clear all runtime context data."""
        self.runtime_context = {}

    def remove_context(self, key: str) -> bool:
        """Remove a specific context key. Returns True if key existed."""
        if key in self.runtime_context:
            del self.runtime_context[key]
            return True
        return False

    def has_context(self, key: str) -> bool:
        """Check if a context key exists."""
        return key in self.runtime_context

    def update_context(self, context_dict: dict) -> None:
        """Update multiple context values at once."""
        self.runtime_context.update(context_dict)

