"""
Universal Tool System
Dynamic, AI-powered tool interface that can adapt any function into a tool.
"""

from typing import Callable, Dict, Any, Optional
import json
import logging
import inspect

logger = logging.getLogger(__name__)

class Tool:
    """
    Universal tool class that can wrap any function.
    Completely dynamic with AI-enhanced capabilities.
    """
    def __init__(self, name: str, description: str, execute_func: Callable,
                 schema: Dict[str, Any] = None, category: str = "general",
                 ai_enhanced: bool = True):
        self.name = name
        self.description = description
        self.execute = execute_func
        self.schema = schema or self._generate_schema()
        self.category = category
        self.ai_enhanced = ai_enhanced

    def _generate_schema(self) -> Dict[str, Any]:
        """Generate schema from function signature."""
        try:
            sig = inspect.signature(self.execute)
            properties = {}
            required = []

            for param_name, param in sig.parameters.items():
                properties[param_name] = {
                    "type": "string",
                    "description": f"Parameter: {param_name}"
                }
                if param.default == inspect.Parameter.empty:
                    required.append(param_name)

            return {
                "type": "object",
                "properties": properties,
                "required": required
            }
        except:
            return {}

    @classmethod
    def from_function(cls, func: Callable, name: str = None,
                     description: str = None, **kwargs) -> 'Tool':
        """Create a Tool from any function."""
        tool_name = name or getattr(func, '__name__', None)
        if not tool_name:
            raise ValueError("Tool name must be provided or function must have __name__ attribute")

        tool_desc = description or func.__doc__
        if not tool_desc:
            raise ValueError(f"Tool description must be provided for tool '{tool_name}'")

        return cls(
            name=tool_name,
            description=tool_desc,
            execute_func=func,
            **kwargs
        )



# No hardcoded example tools - all tools should be provided dynamically by users

# Universal tool creation helpers

def create_tool_from_function(func: Callable, name: str = None,
                             description: str = None, **kwargs) -> Tool:
    """Create a tool from any function with AI-enhanced metadata."""
    return Tool.from_function(func, name, description, **kwargs)

def create_tools_from_module(module_name: str) -> list:
    """Create tools from all functions in a module."""
    import importlib
    tools = []
    try:
        module = importlib.import_module(module_name)
        for name in dir(module):
            if not name.startswith('_'):
                attr = getattr(module, name)
                if callable(attr):
                    tools.append(Tool.from_function(attr, name))
    except Exception as e:
        logger.debug(f"Could not create tools from module {module_name}: {e}")
    return tools

def create_tools_from_object(obj: Any) -> list:
    """Create tools from all methods of an object."""
    tools = []
    for name in dir(obj):
        if not name.startswith('_'):
            attr = getattr(obj, name)
            if callable(attr):
                tool_name = f"{obj.__class__.__name__}.{name}"
                tools.append(Tool.from_function(attr, tool_name))
    return tools

def register_function_as_tool(registry, func: Callable, **kwargs):
    """Register any function as a tool in the registry."""
    tool = Tool.from_function(func, **kwargs)
    registry.register_tool(tool)
    return tool

# No hardcoded example tools - users provide their own tools dynamically
