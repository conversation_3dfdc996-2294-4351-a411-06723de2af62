#!/usr/bin/env python3
"""
AI Utility Orchestrator - Getting Started Script

This script demonstrates how to use the AI Utility Orchestrator package
with your own tools and configuration.

Run this script to see the package in action!
"""

import os
import json
from ai_utility_orchestrator import agent_executor, ToolRegistry, Tool

def setup_example_tools():
    """Create some example tools to demonstrate the package."""
    
    def calculator(params):
        """Perform basic mathematical calculations."""
        expression = params.get("expression", "0")
        try:
            # Simple evaluation for demo (use safer methods in production)
            result = eval(expression.replace("^", "**"))  # Handle exponents
            return f"Calculation result: {result}"
        except Exception as e:
            return f"Calculation error: {e}"
    
    def text_analyzer(params):
        """Analyze text and provide statistics."""
        text = params.get("text", "")
        if not text:
            return "No text provided for analysis"
        
        words = text.split()
        chars = len(text)
        sentences = text.count('.') + text.count('!') + text.count('?')
        
        return f"""Text Analysis:
- Characters: {chars}
- Words: {len(words)}
- Sentences: {sentences}
- Average word length: {chars/len(words):.1f} characters"""
    
    def greeting_generator(params):
        """Generate personalized greetings."""
        name = params.get("name", "Friend")
        style = params.get("style", "casual")
        
        greetings = {
            "casual": f"Hey {name}! How's it going?",
            "formal": f"Good day, {name}. I hope you are well.",
            "friendly": f"Hello there, {name}! Great to see you!",
            "professional": f"Greetings, {name}. How may I assist you today?"
        }
        
        return greetings.get(style, greetings["casual"])
    
    return [
        {
            "name": "calculator",
            "description": "Performs mathematical calculations with basic operators (+, -, *, /, ^)",
            "execute_func": calculator,
            "schema": {
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string", 
                        "description": "Mathematical expression to evaluate (e.g., '2 + 3 * 4')"
                    }
                },
                "required": ["expression"]
            }
        },
        {
            "name": "text_analyzer",
            "description": "Analyzes text and provides word count, character count, and other statistics",
            "execute_func": text_analyzer,
            "schema": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Text to analyze"
                    }
                },
                "required": ["text"]
            }
        },
        {
            "name": "greeting_generator",
            "description": "Generates personalized greetings in different styles",
            "execute_func": greeting_generator,
            "schema": {
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Name of the person to greet"
                    },
                    "style": {
                        "type": "string",
                        "description": "Greeting style: casual, formal, friendly, or professional"
                    }
                },
                "required": ["name"]
            }
        }
    ]

def create_example_config():
    """Create a complete example configuration."""
    return {
        "llm": {
            "model": "gpt-4o-mini",
            "temperature": 0.7
        },
        "system_prompt": "You are a helpful AI assistant that can route user queries to appropriate tools. Analyze requests carefully and select the best tool, or provide direct responses when no tool is needed.",
        "context_limit": 5,
        "context_format": {
            "user_role": "user",
            "assistant_role": "assistant",
            "include_metadata": False
        },
        
        # Required prompt templates
        "no_tools_prompt": """I don't have any tools available right now, but I can still help you directly.

User request: {user_input}

Please respond with JSON:
{{"selected_tool": "none", "parameters": {{}}, "reasoning": "No tools available", "confidence": 1.0, "direct_response": "I'd be happy to help you with that! While I don't have specific tools available, I can provide information and guidance based on your request."}}""",

        "tool_selection_prompt": """Analyze the user's request and determine the best approach.

Available tools:
{tools_text}

User request: {user_input}{context_text}

Please select the most appropriate tool or provide a direct response. Consider:
1. Does the user's request match any available tool's capabilities?
2. Are the required parameters clear from the request?
3. Would a direct response be more appropriate?

Respond with JSON only:
{{"selected_tool": "tool_name_or_none", "parameters": {{}}, "reasoning": "brief_explanation", "confidence": 0.95, "direct_response": "response_if_no_tool_needed"}}""",

        "tool_description_template": "• {name}: {description}",
        "tool_params_template": " (Parameters: {params})",
        "context_available_text": "\n\nPrevious conversation context is available.",
        
        # Optional response formatting
        "final_response_prompt": """User asked: {user_input}
Tool used: {selected_tool}
Tool result: {tool_result}

Please provide a natural, helpful response to the user based on this information.""",
        
        # Error handling
        "error_response_template": "I encountered an issue with the {tool_name} tool: {error}. Let me try to help you in another way.",
        "tool_not_found_message": "I couldn't find the '{selected_tool}' tool. Let me help you directly instead.",
        "llm_error_message": "I'm having trouble processing your request right now. Please try rephrasing your question.",
        "empty_response_message": "I didn't receive a proper response. Could you please try asking your question again?",
        "general_error_message": "An unexpected error occurred: {error}. Please try again.",
        
        # Tools will be added dynamically
        "tools": []
    }

def main():
    """Main demonstration function."""
    print("🚀 AI Utility Orchestrator - Getting Started Demo")
    print("=" * 60)
    
    # Check for OpenAI API key
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ Please set your OPENAI_API_KEY environment variable")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        return
    
    # Create configuration with example tools
    print("📋 Setting up configuration and tools...")
    config = create_example_config()
    config["tools"] = setup_example_tools()
    
    print(f"✅ Configuration ready with {len(config['tools'])} tools")
    
    # Example queries to demonstrate different capabilities
    test_queries = [
        "Calculate 15 * 8 + 12",
        "Analyze this text: 'Hello world! This is a sample text for analysis. How are you today?'",
        "Generate a formal greeting for John",
        "What's the weather like?",  # This should get a direct response
    ]
    
    print("\n🧪 Running example queries...")
    print("-" * 40)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Query: '{query}'")
        
        try:
            result = agent_executor(
                user_input=query,
                config=config,
                user_id="demo_user"
            )
            
            print(f"   🤖 Response: {result['final_response']}")
            print(f"   🔧 Tool used: {result.get('selected_tool', 'none')}")
            
            if result.get('reasoning'):
                print(f"   💭 Reasoning: {result['reasoning']}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Demo complete! Your AI Utility Orchestrator is working perfectly.")
    print("\n📚 Next steps:")
    print("1. Check out USAGE_GUIDE.md for detailed documentation")
    print("2. Customize example_config.json for your needs")
    print("3. Add your own tools and integrate into your project")
    print("4. Run test_dynamic_config.py to verify your setup")

if __name__ == "__main__":
    main()
