Metadata-Version: 2.4
Name: ai_utility_orchestrator
Version: 0.1.0
Summary: A universal, AI-powered utility orchestration package with dynamic LLM-driven routing.
Author: <PERSON><PERSON><PERSON>
License: MIT
Keywords: ai,orchestration,llm,dynamic,tools,automation
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: openai
Requires-Dist: python-dotenv
Requires-Dist: PyYAML
Requires-Dist: importlib-resources>=1.3.0; python_version < "3.9"
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: pytest-mock>=3.10.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=7.0.0; extra == "test"
Requires-Dist: pytest-cov>=4.0.0; extra == "test"
Requires-Dist: pytest-mock>=3.10.0; extra == "test"

# AI Utility Orchestrator

AI Utility Orchestrator is a **completely dynamic**, AI-powered orchestration framework with **zero hardcoding**. It routes user queries to appropriate tools using LLMs, with everything provided by the user at runtime.

## 🚀 Key Features

- **100% Dynamic**: No hardcoded models, prompts, tools, or configurations
- **Universal**: Works with any AI project without modification
- **User-Controlled**: Everything (model, tools, prompts) provided by user
- **AI-Powered**: Intelligent tool selection and response generation
- **Context-Aware**: Session-level state management
- **Modular**: Clean, extensible architecture
- **Zero Dependencies on Defaults**: Requires explicit user configuration

## Installation

### From Source

Clone the repository and install:

```bash
git clone https://github.com/your-org/ai_utility_orchestrator.git
cd ai_utility_orchestrator

# Install as a package
pip install .

# For development with testing dependencies
pip install -e ".[dev]"
```

### From PyPI (when published)

```bash
pip install ai-utility-orchestrator
```

## 🔥 Dynamic Usage (No Hardcoding)

```python
from ai_utility_orchestrator import agent_executor

# User provides EVERYTHING - no defaults, no hardcoding
def my_calculator(params):
    expression = params.get("expression", "0")
    return f"Result: {eval(expression)}"

# User's dynamic configuration (see example_config.json for complete template)
config = {
    "llm": {
        "model": "gpt-4o-mini",  # User chooses model
        "temperature": 0.7       # User sets temperature
    },
    "system_prompt": "You are my personal AI assistant.",
    "context_limit": 5,
    "context_format": {
        "user_role": "user",
        "assistant_role": "assistant",
        "include_metadata": false
    },
    # All prompt templates must be provided (see example_config.json)
    "no_tools_prompt": "Analyze this request: {user_input}...",
    "tool_selection_prompt": "Available tools: {tools_text}...",
    "tool_description_template": "• {name}: {description}",
    "tools": [
        {
            "name": "calculator",
            "description": "Performs calculations",
            "execute_func": "my_module.my_calculator",  # User's function
            "schema": {
                "type": "object",
                "properties": {
                    "expression": {"type": "string"}
                },
                "required": ["expression"]
            }
        }
    ]
}

# Execute with user's configuration
result = agent_executor("Calculate 15 * 8", config=config)
print(result['final_response'])
print(f"Response: {result['final_response']}")
print(f"Tool used: {result['selected_tool']}")

# Example with runtime context
from ai_utility_orchestrator.utils import ContextManager

# Create context manager for session state
cm = ContextManager()
cm.set_context("user_preferences", {"theme": "dark", "language": "en"})

# Your tools can now access runtime context via params["context_manager"]
```

## ⚙️ Configuration (User-Provided Only)

**No default configurations exist.** Users must provide all configuration:

```python
# Complete required configuration
config = {
    "llm": {
        "model": "gpt-4o-mini",              # Required: Your chosen model
        "temperature": 0.7                   # Required: Temperature setting
    },
    "system_prompt": "Your AI assistant prompt",  # Required: System prompt
    "context_limit": 5,                      # Required: Context message limit
    "context_format": {                      # Required: Context formatting
        "user_role": "user",
        "assistant_role": "assistant",
        "include_metadata": false
    },

    # Required prompt templates (see example_config.json for full templates)
    "no_tools_prompt": "Your no-tools template with {user_input}",
    "tool_selection_prompt": "Your tool selection template with {user_input}, {tools_text}, {context_text}",
    "tool_description_template": "• {name}: {description}",

    # Optional error handling templates
    "error_response_template": "Error with {tool_name}: {error}",
    "llm_error_message": "Processing error occurred",

    "tools": []  # Optional: Your tools
}

# Load from file (recommended)
from ai_utility_orchestrator.utils.toolkit import ConfigUtils
config = ConfigUtils.load_config("your_config.json")

# Or set environment variable
# AI_ORCHESTRATOR_CONFIG=/path/to/your/config.json

# See example_config.json for a complete configuration template
```

## Configuration Template

A complete configuration template is provided in `example_config.json` showing all required and optional settings. Copy this file and customize it for your needs.

## Data Storage

- Chat history is stored in `~/.ai_utility_orchestrator/chat_history.json`
- You can customize the storage location in the configuration

## Testing

After installing the package, you can test the entire functionality with:

```bash
# Install with test dependencies
pip install ai_utility_orchestrator[test]

# Run the comprehensive test suite
pytest -v

# Or run the specific test file
pytest tests/test_ai_utility_orchestrator.py -v
```

The test suite includes:
- Package import validation
- Tool creation and registry functionality
- Configuration loading and management
- Context management and persistence
- Response formatting and LLM integration
- Complete end-to-end workflow testing
- Error handling scenarios

All tests use mocking to avoid external API dependencies, so they can run offline.

## Runtime Context

The package includes a powerful runtime context system for session-level state management:

```python
from ai_utility_orchestrator.utils import ContextManager

# Create a context manager
cm = ContextManager()

# Set context values
cm.set_context("user_id", "user123")
cm.set_context("preferences", {"theme": "dark"})

# Get context values
user_id = cm.get_context("user_id")
theme = cm.get_context("preferences", {}).get("theme")

# Check if context exists
if cm.has_context("user_id"):
    print(f"User: {cm.get_context('user_id')}")

# Update multiple values
cm.update_context({
    "session_id": "sess_123",
    "last_action": "login"
})

# Get all context
all_context = cm.get_all_context()

# Remove specific context
cm.remove_context("last_action")

# Clear all context
cm.clear_context()
```

When using the agent_executor, the context_manager is automatically passed to tools:

```python
def my_tool(params):
    # Access context manager from params
    context_manager = params.get("context_manager")
    if context_manager:
        # Get data from context
        user_id = context_manager.get_context("user_id")
        # Store data in context
        context_manager.set_context("last_tool", "my_tool")
    return "Tool executed"
```
