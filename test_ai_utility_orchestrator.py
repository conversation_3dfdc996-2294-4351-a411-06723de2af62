#!/usr/bin/env python3
"""
AI Utility Orchestrator - Comprehensive Test Suite
==================================================

Tests all core functionality of the AI Utility Orchestrator package:
- Package imports and basic functionality
- Tool creation and registration
- Agent execution with different configurations
- Context management
- Error handling
- Multi-tool scenarios

Author: <PERSON><PERSON><PERSON>
"""

import pytest
import json
import os
from unittest.mock import Mock, patch, MagicMock
from ai_utility_orchestrator import (
    agent_executor,
    ToolRegistry,
    ContextManager,
    create_tool_from_function,
    Tool
)


class TestPackageImports:
    """Test that all required components can be imported successfully."""
    
    def test_core_imports(self):
        """Test core component imports."""
        from ai_utility_orchestrator import agent_executor, ToolRegistry
        assert callable(agent_executor)
        assert ToolRegistry is not None
    
    def test_tool_imports(self):
        """Test tool-related imports."""
        from ai_utility_orchestrator import create_tool_from_function, Tool
        assert callable(create_tool_from_function)
        assert Tool is not None
    
    def test_utility_imports(self):
        """Test utility imports."""
        from ai_utility_orchestrator import ContextManager
        assert ContextManager is not None


class TestToolCreation:
    """Test tool creation and registration functionality."""
    
    def test_simple_tool_creation(self):
        """Test creating a simple tool from a function."""
        def sample_tool(params):
            return f"Processed: {params.get('input', 'none')}"
        
        tool = create_tool_from_function(
            sample_tool,
            name="sample_tool",
            description="A sample tool for testing"
        )
        
        assert tool.name == "sample_tool"
        assert tool.description == "A sample tool for testing"
        assert callable(tool.execute)
    
    def test_tool_with_schema(self):
        """Test creating a tool with custom schema."""
        def calculator(params):
            expression = params.get("expression", "0")
            return f"Result: {eval(expression)}"
        
        schema = {
            "type": "object",
            "properties": {
                "expression": {"type": "string"}
            },
            "required": ["expression"]
        }
        
        tool = create_tool_from_function(
            calculator,
            name="calculator",
            description="Performs calculations",
            schema=schema
        )
        
        assert tool.schema == schema
        result = tool.execute({"expression": "2 + 2"})
        assert "4" in result
    
    def test_tool_registry(self):
        """Test tool registry functionality."""
        registry = ToolRegistry()
        
        def test_func(params):
            return "test result"
        
        tool = create_tool_from_function(test_func, name="test_tool", description="Test tool")
        registry.register_tool(tool)
        
        assert "test_tool" in registry.list_tools()
        retrieved_tool = registry.get_tool("test_tool")
        assert retrieved_tool.name == "test_tool"


class TestContextManager:
    """Test context management functionality."""
    
    def test_context_creation(self):
        """Test creating and using context manager."""
        cm = ContextManager()
        
        # Set context
        cm.set_context("user_id", "test_user")
        cm.set_context("preferences", {"theme": "dark"})
        
        # Get context
        assert cm.get_context("user_id") == "test_user"
        assert cm.get_context("preferences")["theme"] == "dark"
        assert cm.get_context("nonexistent") is None
    
    def test_context_operations(self):
        """Test various context operations."""
        cm = ContextManager()
        
        # Set multiple contexts
        cm.set_context("key1", "value1")
        cm.set_context("key2", {"nested": "data"})
        
        # Get all context
        all_context = cm.get_all_context()
        assert "key1" in all_context
        assert "key2" in all_context
        
        # Remove context
        cm.remove_context("key1")
        assert cm.get_context("key1") is None
        assert cm.get_context("key2") is not None


class TestAgentExecution:
    """Test agent execution with mocked LLM responses."""
    
    def create_test_config(self):
        """Create a test configuration."""
        return {
            "llm": {
                "model": "gpt-4o-mini",
                "temperature": 0.7
            },
            "system_prompt": "You are a test assistant.",
            "context_limit": 5,
            "context_format": {
                "user_role": "user",
                "assistant_role": "assistant",
                "include_metadata": False
            },
            "no_tools_prompt": "No tools available for: {user_input}",
            "tool_selection_prompt": """
            Available tools: {tools_text}
            User request: "{user_input}"
            Select tool in JSON: {{"selected_tool": "tool_name", "parameters": {{}}, "reasoning": "explanation"}}
            """,
            "tool_description_template": "• {name}: {description}",
            "tools": []
        }
    
    @patch('ai_utility_orchestrator.utils.response_formatter.format_response')
    def test_agent_execution_no_tools(self, mock_format_response):
        """Test agent execution when no tools are available."""
        config = self.create_test_config()
        
        # Mock LLM response
        mock_format_response.return_value = {
            "parsed_response": "I understand your request but no tools are available.",
            "raw_response": "I understand your request but no tools are available."
        }
        
        result = agent_executor(
            user_input="Hello, how are you?",
            config=config,
            user_id="test_user"
        )
        
        assert result["input"] == "Hello, how are you?"
        assert result["selected_tool"] == "none"
        assert result["user_id"] == "test_user"
        assert "final_response" in result
    
    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    @patch('ai_utility_orchestrator.utils.response_formatter.format_response')
    def test_agent_execution_with_tools(self, mock_format_response, mock_client):
        """Test agent execution with available tools."""
        config = self.create_test_config()

        # Create test tool
        def test_calculator(params):
            return f"Calculated: {params.get('expression', '0')}"

        tool = create_tool_from_function(
            test_calculator,
            name="calculator",
            description="Performs calculations"
        )
        config["tools"] = [tool]

        # Mock the OpenAI client to prevent real API calls
        mock_client.chat.completions.create.return_value = Mock()

        # Mock LLM responses - return the raw JSON string that will be parsed
        mock_format_response.side_effect = [
            {  # Tool selection response
                "parsed_response": {"selected_tool": "calculator", "parameters": {"expression": "2+2"}, "reasoning": "User wants calculation"},
                "raw_response": '{"selected_tool": "calculator", "parameters": {"expression": "2+2"}, "reasoning": "User wants calculation"}'
            },
            {  # Final response formatting
                "parsed_response": "The calculation result is: Calculated: 2+2",
                "raw_response": "The calculation result is: Calculated: 2+2"
            }
        ]

        result = agent_executor(
            user_input="Calculate 2+2",
            config=config,
            user_id="test_user"
        )

        assert result["selected_tool"] == "calculator"
        assert "2+2" in str(result["tool_parameters"])
        assert "Calculated" in result["final_response"]
    
    def test_agent_execution_missing_config(self):
        """Test agent execution with missing configuration."""
        with pytest.raises(ValueError, match="Configuration must be provided"):
            agent_executor("test input", config=None)
    
    def test_agent_execution_invalid_config(self):
        """Test agent execution with invalid configuration."""
        invalid_config = {"invalid": "config"}
        
        with pytest.raises(ValueError, match="LLM configuration must be provided"):
            agent_executor("test input", config=invalid_config)


class TestErrorHandling:
    """Test error handling scenarios."""
    
    def test_tool_execution_error(self):
        """Test handling of tool execution errors."""
        def failing_tool(params):
            raise Exception("Tool execution failed")
        
        tool = create_tool_from_function(
            failing_tool,
            name="failing_tool",
            description="A tool that fails"
        )
        
        # Test that tool creation succeeds but execution fails gracefully
        assert tool.name == "failing_tool"
        
        with pytest.raises(Exception):
            tool.execute({})
    
    def test_invalid_tool_creation(self):
        """Test invalid tool creation scenarios."""
        def test_func(params):
            return "test"

        # Test missing name - should use function name if not provided
        tool = create_tool_from_function(test_func, name=None, description="test")
        assert tool.name == "test_func"  # Uses function name

        # Test missing description - should raise error
        with pytest.raises(ValueError):
            create_tool_from_function(test_func, name="test", description=None)


class TestIntegrationScenarios:
    """Test realistic integration scenarios."""
    
    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    @patch('ai_utility_orchestrator.utils.response_formatter.format_response')
    def test_multi_tool_scenario(self, mock_format_response, mock_client):
        """Test scenario with multiple tools available."""
        config = {
            "llm": {"model": "gpt-4o-mini", "temperature": 0.7},
            "system_prompt": "You are a helpful assistant.",
            "context_limit": 5,
            "context_format": {"user_role": "user", "assistant_role": "assistant", "include_metadata": False},
            "no_tools_prompt": "No tools for: {user_input}",
            "tool_selection_prompt": "Tools: {tools_text}\nUser: {user_input}\nJSON response:",
            "tool_description_template": "• {name}: {description}",
            "tools": []
        }

        # Create multiple tools
        def calculator(params):
            return f"Math result: {params.get('expression')}"

        def weather(params):
            return f"Weather in {params.get('city', 'Unknown')}: Sunny"

        tools = [
            create_tool_from_function(calculator, name="calculator", description="Math calculations"),
            create_tool_from_function(weather, name="weather", description="Weather information")
        ]
        config["tools"] = tools

        # Mock the OpenAI client
        mock_client.chat.completions.create.return_value = Mock()

        # Mock tool selection for weather
        mock_format_response.side_effect = [
            {
                "parsed_response": {"selected_tool": "weather", "parameters": {"city": "New York"}, "reasoning": "User wants weather"},
                "raw_response": '{"selected_tool": "weather", "parameters": {"city": "New York"}, "reasoning": "User wants weather"}'
            },
            {
                "parsed_response": "The weather information shows: Weather in New York: Sunny",
                "raw_response": "The weather information shows: Weather in New York: Sunny"
            }
        ]

        result = agent_executor(
            user_input="What's the weather in New York?",
            config=config
        )

        assert result["selected_tool"] == "weather"
        assert "New York" in str(result["tool_parameters"])
        assert "Sunny" in result["final_response"]
    
    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_context_aware_execution(self, mock_client):
        """Test execution with context manager."""
        config = {
            "llm": {"model": "gpt-4o-mini", "temperature": 0.7},
            "system_prompt": "You are a helpful assistant.",
            "context_limit": 5,
            "context_format": {"user_role": "user", "assistant_role": "assistant", "include_metadata": False},
            "no_tools_prompt": "No tools for: {user_input}",
            "tool_selection_prompt": "Tools: {tools_text}\nUser: {user_input}\nJSON response:",
            "tool_description_template": "• {name}: {description}",
            "tools": []
        }

        def context_tool(params):
            cm = params.get("context_manager")
            if cm:
                user_prefs = cm.get_context("preferences", {})
                return f"Context-aware response with theme: {user_prefs.get('theme', 'default')}"
            return "No context available"

        tool = create_tool_from_function(context_tool, name="context_tool", description="Context-aware tool")
        config["tools"] = [tool]

        # Create context manager
        cm = ContextManager()
        cm.set_context("preferences", {"theme": "dark"})

        # Mock the OpenAI client
        mock_client.chat.completions.create.return_value = Mock()

        with patch('ai_utility_orchestrator.utils.response_formatter.format_response') as mock_format:
            mock_format.side_effect = [
                {
                    "parsed_response": {"selected_tool": "context_tool", "parameters": {}, "reasoning": "Using context tool"},
                    "raw_response": '{"selected_tool": "context_tool", "parameters": {}, "reasoning": "Using context tool"}'
                },
                {
                    "parsed_response": "Context-aware response with theme: dark",
                    "raw_response": "Context-aware response with theme: dark"
                }
            ]

            result = agent_executor(
                user_input="Use context",
                config=config,
                context_manager=cm
            )

            assert "dark" in result["final_response"]


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
