# core/agent_builder.py

import logging
import json
import re
from typing import Dict, Any, Optional, List

from ..utils.response_formatter import format_response
from ..utils.toolkit import ConfigUtils
from ..core.agent_registry import ToolRegistry
from ..core.tools import Tool
from ..utils.context_manager import ContextManager

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


def _get_llm_settings(config: Dict[str, Any]) -> tuple:
    llm_config = config.get("llm", {})
    return llm_config.get("model"), llm_config.get("temperature")


def _build_tool_selection_prompt(user_input: str, tools: list, context_messages: list = None, config: dict = None) -> str:
    config = config or {}

    if not tools:
        no_tools_prompt = config.get("no_tools_prompt")
        if not no_tools_prompt:
            raise ValueError("no_tools_prompt must be provided in config when no tools are available")
        return no_tools_prompt.format(user_input=user_input)

    # Tool description template must be provided by user
    tool_description_template = config.get("tool_description_template")
    if not tool_description_template:
        raise ValueError("tool_description_template must be provided in config")

    tool_descriptions = []
    for tool in tools:
        desc = tool_description_template.format(
            name=tool.name,
            description=tool.description
        )
        if hasattr(tool, 'schema') and tool.schema and tool.schema.get('properties'):
            params = list(tool.schema['properties'].keys())
            if params:
                tool_params_template = config.get("tool_params_template")
                if tool_params_template:
                    desc += tool_params_template.format(params=", ".join(params))
        tool_descriptions.append(desc)

    tools_text = "\n".join(tool_descriptions)
    context_text = ""
    if context_messages:
        context_available_text = config.get("context_available_text")
        if context_available_text:
            context_text = context_available_text

    # Tool selection prompt must be provided by user
    tool_selection_prompt = config.get("tool_selection_prompt")
    if not tool_selection_prompt:
        raise ValueError("tool_selection_prompt must be provided in config")

    return tool_selection_prompt.format(
        user_input=user_input,
        tools_text=tools_text,
        context_text=context_text
    )


def _enhance_tool_parameters(params: Dict[str, Any], tool: Tool, config: dict, model: str, temperature: float) -> Dict[str, Any]:
    if not config.get("enable_parameter_enhancement", False):
        return params

    try:
        parameter_enhancement_prompt = config.get("parameter_enhancement_prompt")
        if not parameter_enhancement_prompt:
            logger.warning("Parameter enhancement enabled but no parameter_enhancement_prompt provided in config")
            return params

        prompt = parameter_enhancement_prompt.format(
            tool_name=tool.name,
            tool_description=tool.description,
            current_parameters=json.dumps(params, indent=2),
            original_query=params.get('original_query', 'N/A')
        )
        result = format_response(
            prompt=prompt,
            formatter="json",
            model_name=model,
            temperature=temperature,
            return_meta=True
        )
        if result and not result.get("error"):
            enhanced = result.get("parsed_response", params)
            if isinstance(enhanced, dict):
                logger.info(f"🔧 Enhanced tool parameters: {enhanced}")
                return enhanced
    except Exception as e:
        logger.warning(f"Parameter enhancement failed: {e}")
    return params


def _ai_error_recovery(error: str, tool_name: str, user_input: str, registry: ToolRegistry, config: dict, model: str, temperature: float) -> str:
    if not config.get("enable_ai_error_recovery", False):
        error_response_template = config.get("error_response_template")
        if not error_response_template:
            return f"Error occurred with {tool_name}: {error}"
        return error_response_template.format(tool_name=tool_name, error=error)

    try:
        alternatives = [t.name for t in registry.get_tools() if t.name != tool_name]
        error_recovery_prompt = config.get("error_recovery_prompt")
        if not error_recovery_prompt:
            logger.warning("AI error recovery enabled but no error_recovery_prompt provided in config")
            error_response_template = config.get("error_response_template")
            if not error_response_template:
                return f"Error occurred with {tool_name}: {error}"
            return error_response_template.format(tool_name=tool_name, error=error)

        recovery_prompt = error_recovery_prompt.format(
            user_input=user_input,
            tool_name=tool_name,
            error=error,
            alternatives=", ".join(alternatives[:3])
        )

        recovery_result = format_response(
            prompt=recovery_prompt,
            formatter="answer",
            model_name=model,
            temperature=temperature
        )

        if recovery_result:
            return recovery_result
        else:
            error_response_template = config.get("error_response_template")
            if not error_response_template:
                return f"Tool {tool_name} encountered an error: {error}"
            return error_response_template.format(tool_name=tool_name, error=error)

    except Exception as e:
        logger.warning(f"AI error recovery failed: {e}")
        error_response_template = config.get("error_response_template")
        if not error_response_template:
            return f"Tool {tool_name} encountered an error: {error}"
        return error_response_template.format(tool_name=tool_name, error=error)





def _extract_tool_decision(llm_response: str, config: dict = None) -> Dict[str, Any]:
    logger.info(f"Processing LLM response: {llm_response[:200]}...")
    if not llm_response or not llm_response.strip():
        empty_response_message = config.get("empty_response_message") if config else None
        if not empty_response_message:
            empty_response_message = "I didn't receive a proper response. Please try again."
        return {
            "selected_tool": "none",
            "parameters": {},
            "reasoning": "Empty response from LLM",
            "direct_response": empty_response_message
        }

    cleaned_response = llm_response.strip()
    patterns = (config or {}).get("json_extraction", {}).get("patterns", [
        r'\{.*?\}',
        r'```json\s*(\{.*?\})\s*```',
        r'\{[^{}]*"selected_tool"[^{}]*\}',
    ])

    for pattern in patterns:
        try:
            match = re.search(pattern, cleaned_response, re.DOTALL)
            if match:
                json_str = match.group(1) if 'group(1)' in pattern else match.group()
                result = json.loads(json_str)
                if isinstance(result, dict) and "selected_tool" in result:
                    return result
        except Exception as e:
            logger.debug(f"Pattern {pattern} failed: {e}")

    return {
        "selected_tool": "none",
        "parameters": {},
        "reasoning": "Could not parse LLM response",
        "direct_response": cleaned_response
    }


def agent_executor(user_input: str, config=None, user_id: str = "default_user",
                   tools: Optional[List] = None, context_manager=None):
    logger.info("🚀 Starting universal AI orchestrator...")

    # User must provide all configuration - no defaults
    if not config:
        raise ValueError("Configuration must be provided by user. No default configuration exists.")

    # Validate required configuration
    if "llm" not in config:
        raise ValueError("LLM configuration must be provided in config['llm']")

    model, temperature = _get_llm_settings(config)
    if not model:
        raise ValueError("Model must be specified in config['llm']['model']")
    if temperature is None:
        raise ValueError("Temperature must be specified in config['llm']['temperature']")

    # System prompt must be provided by user
    system_prompt = config.get("system_prompt")
    if not system_prompt:
        raise ValueError("System prompt must be provided in config['system_prompt']")

    context_manager = context_manager or ContextManager()
    registry = ToolRegistry()

    # Register user-provided tools directly
    if tools:
        for tool_config in tools:
            if isinstance(tool_config, Tool):
                registry.register_tool(tool_config)
            elif isinstance(tool_config, dict):
                # Create tool from configuration
                from .tools import create_tool_from_function
                func_path = tool_config.get("execute_func")
                if func_path and isinstance(func_path, str):
                    # Import function dynamically
                    from ..utils.toolkit import DynamicImportUtils
                    func = DynamicImportUtils.load_object(func_path)
                    if func:
                        tool = create_tool_from_function(
                            func,
                            name=tool_config.get("name"),
                            description=tool_config.get("description"),
                            schema=tool_config.get("schema")
                        )
                        registry.register_tool(tool)
                elif callable(tool_config.get("execute_func")):
                    # Direct function provided
                    tool = create_tool_from_function(
                        tool_config["execute_func"],
                        name=tool_config.get("name"),
                        description=tool_config.get("description"),
                        schema=tool_config.get("schema")
                    )
                    registry.register_tool(tool)

    try:
        # Context configuration must be provided by user
        context_limit = config.get("context_limit")
        context_format = config.get("context_format")

        if context_limit is None:
            raise ValueError("Context limit must be provided in config['context_limit']")
        if context_format is None:
            raise ValueError("Context format must be provided in config['context_format']")

        context_messages = context_manager.get_recent_messages(
            user_id,
            limit=context_limit,
            format_config=context_format
        )

        context_manager.set_context("current_user_id", user_id)
        context_manager.set_context("current_query", user_input)
        context_manager.set_context("execution_timestamp", __import__('time').time())

        tool_prompt = _build_tool_selection_prompt(user_input, registry.get_tools(), context_messages, config)

        decision_result = format_response(
            prompt=tool_prompt,
            formatter="json",
            model_name=model,
            temperature=temperature,
            return_meta=True,
            system_prompt=system_prompt,
            messages=context_messages
        )

        if not decision_result or decision_result.get("error"):
            error = decision_result.get("error", "No response")
            llm_error_message = config.get("llm_error_message")
            if not llm_error_message:
                llm_error_message = "I'm having trouble processing your request."
            tool_decision = {
                "selected_tool": "none",
                "parameters": {},
                "reasoning": f"LLM error: {error}",
                "direct_response": llm_error_message
            }
        else:
            tool_decision = _extract_tool_decision(decision_result.get("raw_response", ""), config)

        selected_tool = tool_decision.get("selected_tool", "none")
        tool_parameters = tool_decision.get("parameters", {})
        reasoning = tool_decision.get("reasoning", "")
        final_response = ""
        tool_result = None

        if selected_tool != "none":
            tool = registry.get_tool(selected_tool)
            if tool:
                try:
                    enhanced_params = {
                        **tool_parameters,
                        "registry": registry,
                        "config": config,
                        "user_id": user_id,
                        "original_query": user_input,
                        "context_manager": context_manager
                    }

                    if config.get("enable_parameter_enhancement", False):
                        enhanced_params = _enhance_tool_parameters(enhanced_params, tool, config, model, temperature)

                    tool_result = tool.execute(enhanced_params)

                    final_response_prompt = config.get("final_response_prompt")
                    if not final_response_prompt:
                        # If no final response prompt provided, use tool result directly
                        final_response = str(tool_result)
                    else:
                        final_prompt = final_response_prompt.format(
                            user_input=user_input,
                            selected_tool=selected_tool,
                            tool_result=tool_result
                        )

                        result = format_response(
                            prompt=final_prompt,
                            formatter="answer",
                            model_name=model,
                            temperature=temperature,
                            return_meta=True,
                            system_prompt=system_prompt
                        )

                        final_response = result.get("parsed_response", str(tool_result))

                except Exception as e:
                    logger.error(f"Tool execution failed: {e}")
                    final_response = _ai_error_recovery(str(e), selected_tool, user_input, registry, config, model, temperature)
            else:
                tool_not_found_message = config.get("tool_not_found_message")
                if not tool_not_found_message:
                    final_response = f"Tool '{selected_tool}' not found."
                else:
                    final_response = tool_not_found_message.format(selected_tool=selected_tool)
        else:
            final_response = tool_decision.get("direct_response", "I can help you with that.")
            logger.info("Providing direct response")

        context_manager.add_interaction(user_id, user_input, final_response)

        return {
            "input": user_input,
            "selected_tool": selected_tool,
            "tool_parameters": tool_parameters,
            "tool_result": tool_result,
            "reasoning": reasoning,
            "final_response": final_response,
            "used_model": model,
            "user_id": user_id
        }

    except Exception as e:
        logger.error(f"Agent execution failed: {e}")
        general_error_message = config.get("general_error_message")
        if not general_error_message:
            fallback = f"An error occurred: {str(e)}"
        else:
            fallback = general_error_message.format(error=str(e))
        try:
            context_manager.add_interaction(user_id, user_input, fallback)
        except Exception:
            logger.error("Failed to store interaction context")

        return {
            "input": user_input,
            "selected_tool": "none",
            "tool_parameters": {},
            "tool_result": None,
            "reasoning": "Unhandled error",
            "final_response": fallback,
            "used_model": model,
            "user_id": user_id,
            "error": str(e)
        }
