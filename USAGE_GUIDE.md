# AI Utility Orchestrator - Complete Usage Guide

## 🚀 Quick Start

### 1. Installation

```bash
# Install the package
pip install ai-utility-orchestrator

# Or install from source
git clone <your-repo>
cd ai_utility_orchestrator
pip install .
```

### 2. Set up OpenAI API Key

```bash
# Set environment variable
export OPENAI_API_KEY="your-api-key-here"

# Or create .env file
echo "OPENAI_API_KEY=your-api-key-here" > .env
```

### 3. Basic Usage

```python
from ai_utility_orchestrator import agent_executor
import json

# Load configuration (see Configuration section below)
with open("my_config.json", "r") as f:
    config = json.load(f)

# Execute a query
result = agent_executor(
    user_input="Calculate 15 * 8",
    config=config,
    user_id="user123"
)

print(result['final_response'])
```

## ⚙️ Configuration Setup

### Step 1: Copy the Template

```bash
# Copy the example configuration
cp example_config.json my_config.json
```

### Step 2: Customize Your Configuration

Edit `my_config.json` with your settings:

```json
{
  "llm": {
    "model": "gpt-4o-mini",
    "temperature": 0.7
  },
  "system_prompt": "You are my AI assistant.",
  "context_limit": 5,
  "context_format": {
    "user_role": "user",
    "assistant_role": "assistant",
    "include_metadata": false
  },
  "tools": []
}
```

### Required Configuration Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `llm.model` | string | OpenAI model name | `"gpt-4o-mini"` |
| `llm.temperature` | number | Creativity level (0-1) | `0.7` |
| `system_prompt` | string | AI behavior instructions | `"You are helpful"` |
| `context_limit` | number | Max conversation history | `5` |
| `context_format` | object | Message formatting rules | See example |
| `no_tools_prompt` | string | Template when no tools available | See example_config.json |
| `tool_selection_prompt` | string | Template for tool selection | See example_config.json |
| `tool_description_template` | string | How to format tool descriptions | `"• {name}: {description}"` |

## 🛠️ Adding Your Own Tools

### Method 1: Function-Based Tools

```python
def my_calculator(params):
    """Calculate mathematical expressions."""
    expression = params.get("expression", "0")
    try:
        result = eval(expression)  # Use safer evaluation in production
        return f"Result: {result}"
    except Exception as e:
        return f"Error: {e}"

def weather_checker(params):
    """Get weather information."""
    location = params.get("location", "Unknown")
    # Your weather API logic here
    return f"Weather in {location}: Sunny, 72°F"

# Add to your config
config = {
    # ... other config ...
    "tools": [
        {
            "name": "calculator",
            "description": "Performs mathematical calculations",
            "execute_func": my_calculator,  # Direct function reference
            "schema": {
                "type": "object",
                "properties": {
                    "expression": {"type": "string", "description": "Math expression to evaluate"}
                },
                "required": ["expression"]
            }
        },
        {
            "name": "weather",
            "description": "Get current weather for a location",
            "execute_func": weather_checker,
            "schema": {
                "type": "object",
                "properties": {
                    "location": {"type": "string", "description": "City name"}
                },
                "required": ["location"]
            }
        }
    ]
}
```

### Method 2: Module Path Tools

```python
# In your_tools.py
def file_reader(params):
    """Read file contents."""
    filepath = params.get("filepath")
    try:
        with open(filepath, 'r') as f:
            return f.read()
    except Exception as e:
        return f"Error reading file: {e}"

# In config
{
    "tools": [
        {
            "name": "file_reader",
            "description": "Read contents of a file",
            "execute_func": "your_tools.file_reader",  # Module path
            "schema": {
                "type": "object",
                "properties": {
                    "filepath": {"type": "string", "description": "Path to file"}
                },
                "required": ["filepath"]
            }
        }
    ]
}
```

### Method 3: Using Tool Registry Directly

```python
from ai_utility_orchestrator import ToolRegistry, Tool, create_tool_from_function

# Create registry
registry = ToolRegistry()

# Create and register tools
def email_sender(params):
    to = params.get("to")
    subject = params.get("subject")
    body = params.get("body")
    # Your email logic here
    return f"Email sent to {to}"

tool = create_tool_from_function(
    email_sender,
    name="email_sender",
    description="Send emails to recipients"
)

registry.register_tool(tool)

# Use registry with agent_executor
result = agent_executor(
    user_input="Send <NAME_EMAIL>",
    config=config,
    tools=registry.get_tools()
)
```

## 🎯 Advanced Usage Patterns

### Context Management

```python
from ai_utility_orchestrator import ContextManager

# Create context manager
cm = ContextManager()

# Set session data
cm.set_context("user_preferences", {"theme": "dark", "language": "en"})
cm.set_context("current_project", "ai_assistant")

# Your tools can access context
def personalized_tool(params):
    context_manager = params.get("context_manager")
    if context_manager:
        prefs = context_manager.get_context("user_preferences", {})
        theme = prefs.get("theme", "light")
        return f"Using {theme} theme for response"
    return "Default response"

# Use with agent_executor
result = agent_executor(
    user_input="Show my dashboard",
    config=config,
    context_manager=cm
)
```

### Multiple Tool Categories

```python
# Organize tools by category
config = {
    # ... other config ...
    "tools": [
        # File operations
        {
            "name": "read_file",
            "description": "Read file contents",
            "execute_func": "file_tools.read_file",
            "category": "file_ops"
        },
        {
            "name": "write_file",
            "description": "Write to file",
            "execute_func": "file_tools.write_file",
            "category": "file_ops"
        },

        # Data processing
        {
            "name": "analyze_data",
            "description": "Analyze CSV data",
            "execute_func": "data_tools.analyze_csv",
            "category": "data"
        },

        # Communication
        {
            "name": "send_email",
            "description": "Send email messages",
            "execute_func": "comm_tools.send_email",
            "category": "communication"
        }
    ]
}
```

### Error Handling and Recovery

```python
config = {
    # ... other config ...
    "enable_ai_error_recovery": True,
    "error_recovery_prompt": """
The tool '{tool_name}' failed with error: {error}

Original request: {user_input}
Available alternatives: {alternatives}

Please suggest a recovery approach or alternative solution.
""",
    "error_response_template": "Tool {tool_name} encountered an issue: {error}. Let me try a different approach."
}
```

### Custom Response Formatting

```python
config = {
    # ... other config ...
    "final_response_prompt": """
User asked: {user_input}
Tool used: {selected_tool}
Raw result: {tool_result}

Please format this as a helpful, conversational response for the user.
Add relevant context and suggestions if appropriate.
""",

    "tool_selection_prompt": """
Analyze the user's request and select the most appropriate tool.

Available tools:
{tools_text}

User request: "{user_input}"
{context_text}

Consider:
1. Which tool best matches the user's intent?
2. Are the required parameters available?
3. Should you provide a direct response instead?

Respond with JSON:
{{"selected_tool": "tool_name_or_none", "parameters": {{}}, "reasoning": "explanation", "confidence": 0.95}}
"""
}
```

## 🔧 Integration Examples

### Flask Web Application

```python
from flask import Flask, request, jsonify
from ai_utility_orchestrator import agent_executor
import json

app = Flask(__name__)

# Load your configuration
with open("config.json", "r") as f:
    config = json.load(f)

@app.route("/chat", methods=["POST"])
def chat():
    data = request.json
    user_input = data.get("message")
    user_id = data.get("user_id", "anonymous")

    try:
        result = agent_executor(
            user_input=user_input,
            config=config,
            user_id=user_id
        )

        return jsonify({
            "response": result["final_response"],
            "tool_used": result["selected_tool"],
            "success": True
        })
    except Exception as e:
        return jsonify({
            "error": str(e),
            "success": False
        }), 500

if __name__ == "__main__":
    app.run(debug=True)
```

### Command Line Interface

```python
#!/usr/bin/env python3
import argparse
import json
from ai_utility_orchestrator import agent_executor

def main():
    parser = argparse.ArgumentParser(description="AI Utility Orchestrator CLI")
    parser.add_argument("--config", required=True, help="Configuration file path")
    parser.add_argument("--user-id", default="cli_user", help="User ID")
    parser.add_argument("query", help="Your query")

    args = parser.parse_args()

    # Load configuration
    with open(args.config, "r") as f:
        config = json.load(f)

    # Execute query
    result = agent_executor(
        user_input=args.query,
        config=config,
        user_id=args.user_id
    )

    print(f"Response: {result['final_response']}")
    print(f"Tool used: {result['selected_tool']}")

if __name__ == "__main__":
    main()
```

### Jupyter Notebook Integration

```python
# In Jupyter notebook cell
from ai_utility_orchestrator import agent_executor
import json

# Load config
with open("notebook_config.json", "r") as f:
    config = json.load(f)

# Interactive function
def ask_ai(question):
    result = agent_executor(
        user_input=question,
        config=config,
        user_id="notebook_user"
    )
    print(f"🤖 {result['final_response']}")
    if result['selected_tool'] != 'none':
        print(f"📊 Used tool: {result['selected_tool']}")

# Usage
ask_ai("Analyze the data in sales.csv")
ask_ai("What's the weather like in New York?")
```

## 🚨 Common Issues and Solutions

### Issue 1: Missing Configuration
```
ValueError: Configuration must be provided by user
```
**Solution**: Always provide a complete configuration object or file.

### Issue 2: Missing Required Fields
```
ValueError: tool_selection_prompt must be provided in config
```
**Solution**: Use `example_config.json` as a template and ensure all required fields are present.

### Issue 3: Tool Function Not Found
```
Could not import your_module.your_function
```
**Solution**: Ensure the module is in your Python path or use direct function references.

### Issue 4: Context Format Error
```
ValueError: format_config must be a dictionary
```
**Solution**: Ensure `context_format` is an object with `user_role`, `assistant_role`, and `include_metadata`.

## 📚 Best Practices

1. **Configuration Management**
   - Keep configurations in version control
   - Use environment-specific configs (dev, prod)
   - Validate configurations before deployment

2. **Tool Design**
   - Make tools focused and single-purpose
   - Include comprehensive error handling
   - Provide clear parameter descriptions

3. **Security**
   - Validate all tool inputs
   - Use safe evaluation methods
   - Implement proper authentication for sensitive tools

4. **Performance**
   - Cache expensive operations in tools
   - Use appropriate context limits
   - Monitor token usage

5. **Testing**
   - Test tools independently
   - Validate configurations
   - Test error scenarios

## 🎉 You're Ready!

Your AI Utility Orchestrator is now ready to use in any project. The package provides complete flexibility while maintaining a clean, professional interface. Users can integrate it into web apps, CLIs, notebooks, or any Python application with full control over behavior and functionality.
```
