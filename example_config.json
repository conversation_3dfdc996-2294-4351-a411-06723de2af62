{"llm": {"model": "gpt-4o-mini", "temperature": 0.7}, "system_prompt": "You are an AI orchestrator that routes user queries to appropriate tools. Analyze requests, select the best tool if available, or provide helpful responses directly.", "context_limit": 5, "context_format": {"user_role": "user", "assistant_role": "assistant", "include_metadata": false}, "enable_parameter_enhancement": false, "enable_ai_error_recovery": true, "no_tools_prompt": "Analyze this user request and provide a helpful direct response since no tools are available:\n\nUser request: {user_input}\n\nRespond with JSON:\n{{\"selected_tool\": \"none\", \"parameters\": {{}}, \"reasoning\": \"No tools available\", \"confidence\": 1.0, \"direct_response\": \"your_helpful_response\"}}", "tool_description_template": "• {name}: {description}", "tool_params_template": " (Parameters: {params})", "context_available_text": "\n\nRecent context available.", "tool_selection_prompt": "Analyze the user's request and determine the best approach.\n\nAvailable tools:\n{tools_text}\n\nUser request: {user_input}{context_text}\n\nRespond with JSON only:\n{{\"selected_tool\": \"tool_name_or_none\", \"parameters\": {{}}, \"reasoning\": \"brief_explanation\", \"confidence\": 0.95, \"direct_response\": \"response_if_no_tool_needed\"}}", "parameter_enhancement_prompt": "You are helping to optimize tool parameters.\n\nTool: {tool_name}\nDescription: {tool_description}\nCurrent parameters: {current_parameters}\nOriginal user query: {original_query}\n\nSuggest any improvements or additions. Return as JSON.", "error_recovery_prompt": "The tool '{tool_name}' failed.\nError: {error}\n\nOriginal request: {user_input}\nAlternatives: {alternatives}\n\nSuggest a recovery or alternative solution.", "final_response_prompt": "User query: {user_input}\nTool used: {selected_tool}\nResult: {tool_result}\n\nRespond naturally to user.", "error_response_template": "Error occurred with {tool_name}: {error}", "tool_not_found_message": "Tool '{selected_tool}' not found.", "llm_error_message": "I'm having trouble processing your request.", "empty_response_message": "I didn't receive a proper response. Please try again.", "general_error_message": "An error occurred: {error}", "json_extraction": {"patterns": ["\\{.*?\\}", "```json\\s*(\\{.*?\\})\\s*```", "\\{[^{}]*\"selected_tool\"[^{}]*\\}"]}, "tools": [{"name": "example_calculator", "description": "Performs basic mathematical calculations", "execute_func": "your_module.calculator_function", "schema": {"type": "object", "properties": {"expression": {"type": "string", "description": "Mathematical expression to evaluate"}}, "required": ["expression"]}}]}