
"""
AI Utility Orchestrator - Universal AI-powered tool orchestration framework.

A completely dynamic, AI-driven orchestration system that adapts to any project.
No hardcoding, fully universal, and intelligently adaptive.

Key Features:
- Universal tool registration from any function
- AI-powered tool discovery and selection
- Dynamic schema generation
- Modular and extensible architecture
- Works with any AI project without modification
"""

# Core orchestration components
from ai_utility_orchestrator.core.agent_builder import agent_executor
from ai_utility_orchestrator.core.agent_registry import ToolRegistry
from ai_utility_orchestrator.core.tools import (
    Tool,
    create_tool_from_function,
    create_tools_from_module,
    create_tools_from_object,
    register_function_as_tool
)

# Utility components
from ai_utility_orchestrator.utils.toolkit import ConfigUtils
from ai_utility_orchestrator.utils.context_manager import ContextManager
from ai_utility_orchestrator.utils.response_formatter import format_response

__version__ = "0.1.0"
__all__ = [
    # Core components
    'agent_executor',
    'ToolRegistry',
    'Tool',

    # Universal tool creation
    'create_tool_from_function',
    'create_tools_from_module',
    'create_tools_from_object',
    'register_function_as_tool',

    # Utilities
    'ConfigUtils',
    'ContextManager',
    'format_response'
]