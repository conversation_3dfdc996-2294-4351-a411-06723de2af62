#!/usr/bin/env python3
"""
Test script to verify the AI Utility Orchestrator works with user-provided configuration.
This demonstrates the completely dynamic, zero-hardcoded approach.
"""

import json
from ai_utility_orchestrator import agent_executor, ToolRegistry, Tool

def test_calculator(params):
    """Example user-provided tool function."""
    expression = params.get("expression", "0")
    try:
        # Simple evaluation for demo (in production, use safer evaluation)
        result = eval(expression)
        return f"Result: {result}"
    except Exception as e:
        return f"Error: {e}"

def test_dynamic_configuration():
    """Test that the package works with completely user-provided configuration."""
    
    # User provides ALL configuration - no defaults
    config = {
        "llm": {
            "model": "gpt-4o-mini",
            "temperature": 0.7
        },
        "system_prompt": "You are a helpful AI assistant that routes queries to tools.",
        "context_limit": 5,
        "context_format": {
            "user_role": "user",
            "assistant_role": "assistant",
            "include_metadata": False
        },
        
        # All prompt templates provided by user
        "no_tools_prompt": "No tools available. Provide direct response for: {user_input}\n\nJSON: {{\"selected_tool\": \"none\", \"parameters\": {{}}, \"reasoning\": \"No tools\", \"confidence\": 1.0, \"direct_response\": \"I can help with that directly.\"}}",
        
        "tool_selection_prompt": "Select best tool:\n\nTools:\n{tools_text}\n\nQuery: {user_input}{context_text}\n\nJSON: {{\"selected_tool\": \"tool_or_none\", \"parameters\": {{}}, \"reasoning\": \"explanation\", \"confidence\": 0.9}}",
        
        "tool_description_template": "• {name}: {description}",
        "tool_params_template": " (Params: {params})",
        "context_available_text": "\n\nContext available.",
        
        # Error handling templates
        "error_response_template": "Tool {tool_name} failed: {error}",
        "llm_error_message": "Processing error occurred",
        "empty_response_message": "No response received",
        "general_error_message": "Error: {error}",
        
        # User's tools
        "tools": [
            {
                "name": "calculator",
                "description": "Performs mathematical calculations",
                "execute_func": test_calculator,  # Direct function reference
                "schema": {
                    "type": "object",
                    "properties": {
                        "expression": {"type": "string", "description": "Math expression"}
                    },
                    "required": ["expression"]
                }
            }
        ]
    }
    
    print("🧪 Testing AI Utility Orchestrator with user-provided configuration...")
    
    try:
        # Test 1: With tools
        print("\n📊 Test 1: Query with available tools")
        result = agent_executor(
            user_input="Calculate 15 * 8 + 2",
            config=config,
            user_id="test_user"
        )
        print(f"✅ Success: {result.get('final_response', 'No response')}")
        print(f"   Tool used: {result.get('selected_tool', 'None')}")
        
        # Test 2: Without tools
        print("\n💬 Test 2: Query without tools")
        config_no_tools = config.copy()
        config_no_tools["tools"] = []
        
        result2 = agent_executor(
            user_input="Hello, how are you?",
            config=config_no_tools,
            user_id="test_user"
        )
        print(f"✅ Success: {result2.get('final_response', 'No response')}")
        print(f"   Tool used: {result2.get('selected_tool', 'None')}")
        
        print("\n🎉 All tests passed! Package is truly dynamic with zero hardcoding.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_missing_config():
    """Test that package properly requires user configuration."""
    print("\n🚫 Testing missing configuration handling...")
    
    try:
        # This should fail - no config provided
        result = agent_executor("test", config=None)
        print("❌ Should have failed but didn't")
        return False
    except ValueError as e:
        print(f"✅ Correctly rejected missing config: {e}")
        return True
    except Exception as e:
        print(f"❌ Wrong error type: {e}")
        return False

if __name__ == "__main__":
    print("🚀 AI Utility Orchestrator - Dynamic Configuration Test")
    print("=" * 60)
    
    # Test dynamic configuration
    success1 = test_dynamic_configuration()
    
    # Test missing configuration handling
    success2 = test_missing_config()
    
    if success1 and success2:
        print("\n🎯 ALL TESTS PASSED - Package is truly dynamic!")
    else:
        print("\n💥 Some tests failed")
